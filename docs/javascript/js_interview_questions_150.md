# JavaScript + ES6面试题库（150道）

## 目录
- [JavaScript基础语法和概念（45道）](#javascript基础语法和概念)
- [ES6+新特性（70道）](#es6新特性)
- [异步编程（25道）](#异步编程)
- [现代JavaScript特性（10道）](#现代javascript特性)

---

## JavaScript基础语法和概念

### 数据类型与变量（15道）

**001. [初级]** 请解释JavaScript中的原始数据类型有哪些？

**002. [初级]** `typeof null` 返回什么值？为什么？

**003. [初级]** 如何判断一个变量是否为数组？

**004. [中级]** 解释`==`和`===`的区别，并举例说明

**005. [初级]** JavaScript中`undefined`和`null`的区别是什么？

**006. [中级]** 什么是类型转换？隐式转换和显式转换有什么区别？

**007. [初级]** `Number('123abc')`和`parseInt('123abc')`的结果分别是什么？

**008. [中级]** 解释JavaScript中的"假值"（falsy values）有哪些？

**009. [初级]** 如何检测一个变量的数据类型？

**010. [中级]** `Object.prototype.toString.call()`方法的作用是什么？

**011. [中级]** JavaScript中的包装对象是什么？

**012. [高级]** 解释JavaScript中的装箱和拆箱操作

**013. [初级]** 什么是NaN？如何检测一个值是否为NaN？

**014. [中级]** `0.1 + 0.2 === 0.3`的结果是什么？为什么？

**015. [中级]** 如何准确地比较两个浮点数是否相等？

### 函数与作用域（15道）

**016. [初级]** 函数声明和函数表达式有什么区别？

**017. [中级]** 什么是函数提升（hoisting）？

**018. [中级]** 解释JavaScript中的作用域链

**019. [初级]** 什么是闭包？请举一个简单的例子

**020. [中级]** 闭包的实际应用场景有哪些？

**021. [高级]** 解释闭包可能导致的内存泄漏问题

**022. [中级]** `this`关键字在不同情况下指向什么？

**023. [中级]** `call`、`apply`、`bind`方法的区别和用法

**024. [初级]** 什么是立即执行函数表达式（IIFE）？

**025. [中级]** 如何实现函数的柯里化（currying）？

**026. [高级]** 什么是尾调用优化？JavaScript支持吗？

**027. [中级]** 函数的`length`属性表示什么？

**028. [初级]** 什么是回调函数？

**029. [中级]** 如何避免回调地狱（callback hell）？

**030. [高级]** 解释函数式编程中的纯函数概念

### 对象与原型（15道）

**031. [初级]** 如何创建一个对象？有几种方式？

**032. [中级]** 什么是原型链？

**033. [中级]** `__proto__`和`prototype`的区别是什么？

**034. [初级]** 如何检查对象是否具有某个属性？

**035. [中级]** `hasOwnProperty`和`in`操作符的区别

**036. [中级]** 如何实现对象的深拷贝？

**037. [高级]** `Object.create()`方法的作用和用法

**038. [中级]** 什么是属性描述符？如何使用？

**039. [初级]** 如何遍历对象的属性？

**040. [中级]** `Object.keys()`、`Object.values()`、`Object.entries()`的区别

**041. [中级]** 如何冻结一个对象？

**042. [高级]** 解释JavaScript中的继承机制

**043. [中级]** 构造函数和普通函数的区别

**044. [中级]** `new`操作符的执行过程是什么？

**045. [高级]** 如何实现一个简单的`new`操作符？

---

## ES6+新特性

### let、const和块级作用域（8道）

**046. [初级]** `let`、`const`和`var`的区别

**047. [初级]** 什么是块级作用域？

**048. [中级]** 什么是暂时性死区（TDZ）？

**049. [中级]** `const`声明的对象可以修改吗？

**050. [初级]** 在什么情况下使用`let`、`const`、`var`？

**051. [中级]** for循环中使用`let`和`var`的区别

**052. [中级]** 如何理解变量提升在ES6中的变化？

**053. [高级]** 解释ES6中函数声明在块级作用域中的行为

### 箭头函数（8道）

**054. [初级]** 箭头函数的基本语法是什么？

**055. [中级]** 箭头函数和普通函数的区别

**056. [中级]** 箭头函数中的`this`指向问题

**057. [初级]** 箭头函数可以作为构造函数吗？

**058. [中级]** 箭头函数没有哪些特性？

**059. [中级]** 什么时候不应该使用箭头函数？

**060. [高级]** 箭头函数在事件处理中的注意事项

**061. [中级]** 如何在箭头函数中访问`arguments`对象？

### 解构赋值（10道）

**062. [初级]** 什么是解构赋值？基本语法是什么？

**063. [初级]** 数组解构赋值的常见用法

**064. [中级]** 对象解构赋值如何设置默认值？

**065. [中级]** 如何解构嵌套对象？

**066. [中级]** 解构赋值在函数参数中的应用

**067. [中级]** 如何交换两个变量的值（使用解构）？

**068. [中级]** 字符串解构赋值的用法

**069. [高级]** 解构赋值的剩余模式（rest pattern）

**070. [中级]** 如何解构赋值并重命名变量？

**071. [中级]** 解构赋值在React props中的应用

### 模板字符串（6道）

**072. [初级]** 什么是模板字符串？如何使用？

**073. [初级]** 模板字符串支持多行吗？

**074. [中级]** 如何在模板字符串中执行函数？

**075. [中级]** 什么是标签模板字符串？

**076. [高级]** 如何实现一个简单的模板字符串处理函数？

**077. [中级]** 模板字符串与字符串拼接的性能比较

### 函数增强（8道）

**078. [初级]** ES6函数参数默认值的用法

**079. [中级]** 剩余参数（rest parameters）和扩展运算符的区别

**080. [中级]** 如何使用扩展运算符合并数组？

**081. [中级]** 扩展运算符在对象中的应用

**082. [中级]** 函数的`name`属性在ES6中的变化

**083. [高级]** 如何使用参数默认值实现必需参数检查？

**084. [中级]** 剩余参数与arguments对象的区别

**085. [中级]** 扩展运算符的实际应用场景

### Class类（12道）

**086. [初级]** ES6中如何定义一个类？

**087. [中级]** 类的构造函数如何定义？

**088. [中级]** 如何实现类的继承？

**089. [中级]** 类中的静态方法如何定义和使用？

**090. [中级]** 类的私有属性如何实现？

**091. [高级]** super关键字的作用和用法

**092. [中级]** 类表达式和类声明的区别

**093. [高级]** 如何在类中定义getter和setter？

**094. [中级]** 类的实例方法和原型方法的区别

**095. [高级]** 类的继承如何实现方法重写？

**096. [中级]** 抽象类在JavaScript中如何模拟？

**097. [高级]** 类的装饰器（decorator）概念及用法

### Symbol数据类型（6道）

**098. [中级]** 什么是Symbol？它的特点是什么？

**099. [中级]** 如何创建Symbol？Symbol的用途有哪些？

**100. [中级]** Symbol.iterator的作用是什么？

**101. [高级]** 如何使用Symbol创建对象的私有属性？

**102. [中级]** Symbol.for()和Symbol()的区别

**103. [中级]** 内置Symbol有哪些？举例说明

### Set和Map（8道）

**104. [初级]** Set数据结构的特点和基本用法

**105. [中级]** 如何使用Set进行数组去重？

**106. [中级]** Map和Object的区别是什么？

**107. [中级]** WeakSet和WeakMap的特点和用途

**108. [中级]** 如何遍历Set和Map？

**109. [高级]** 什么情况下使用WeakMap比Map更合适？

**110. [中级]** Map的键可以是什么类型？

**111. [中级]** 如何将Map转换为数组？

### 迭代器和生成器（4道）

**112. [中级]** 什么是迭代器？如何自定义迭代器？

**113. [中级]** 生成器函数的语法和特点

**114. [高级]** 生成器的实际应用场景有哪些？

**115. [高级]** 如何使用生成器实现无限序列？

---

## 异步编程

### Promise基础（12道）

**116. [初级]** 什么是Promise？它解决了什么问题？

**117. [初级]** Promise有哪几种状态？

**118. [中级]** 如何创建一个Promise？

**119. [中级]** Promise.then()方法的用法

**120. [中级]** Promise的链式调用如何工作？

**121. [中级]** Promise.catch()和try-catch的区别

**122. [中级]** 如何处理多个Promise？

**123. [中级]** Promise.all()和Promise.allSettled()的区别

**124. [高级]** Promise.race()的使用场景

**125. [中级]** 如何取消一个Promise？

**126. [高级]** 如何实现一个简单的Promise？

**127. [中级]** Promise中的错误传播机制

### async/await（8道）

**128. [初级]** async/await的基本用法

**129. [中级]** async函数返回什么？

**130. [中级]** 如何在async函数中处理错误？

**131. [中级]** async/await相比Promise的优势

**132. [中级]** 如何并发执行多个async操作？

**133. [高级]** async/await在循环中的使用注意事项

**134. [中级]** 顶层await的概念和用法

**135. [高级]** 如何实现async/await的polyfill原理？

### 事件循环和微任务（5道）

**136. [中级]** JavaScript的事件循环机制是什么？

**137. [中级]** 宏任务和微任务的区别

**138. [高级]** Promise.resolve()在事件循环中的执行时机

**139. [中级]** setTimeout(0)和Promise.resolve()的执行顺序

**140. [高级]** 如何理解事件循环的执行栈、任务队列和微任务队列？

---

## 现代JavaScript特性

### 模块化（5道）

**141. [中级]** ES6模块与CommonJS模块的区别

**142. [中级]** import和export的各种用法

**143. [中级]** 动态import()的用法和应用场景

**144. [高级]** 模块的循环依赖问题如何解决？

**145. [中级]** Tree-shaking的原理是什么？

### 新API和特性（5道）

**146. [中级]** Proxy对象的作用和基本用法

**147. [高级]** Reflect对象提供了哪些方法？

**148. [中级]** 可选链操作符(?.)的用法

**149. [中级]** 空值合并操作符(??)的作用

**150. [中级]** BigInt数据类型的特点和用法

---

## 题目统计

**总计：150道题目**

### 按难度分布：
- 初级（60道）：40%
- 中级（68道）：45.3%
- 高级（22道）：14.7%

### 按内容分布：
- JavaScript基础语法和概念：45道（30%）
- ES6+新特性：70道（46.7%）
- 异步编程：25道（16.7%）
- 现代JavaScript特性：10道（6.6%）

### 知识点覆盖：
- 数据类型与变量
- 函数与作用域
- 对象与原型
- ES6语法特性
- 异步编程模式
- 现代JavaScript API

**备注：**
- 题目按知识点分类，便于系统学习
- 难度递进，从基础概念到实际应用
- 重点关注企业面试高频考点
- 涵盖现代JavaScript开发必备技能