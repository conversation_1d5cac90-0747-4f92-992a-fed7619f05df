# JavaScript + ES6面试题库（150道）

## 目录
- [JavaScript基础语法和概念（45道）](#javascript基础语法和概念)
- [ES6+新特性（70道）](#es6新特性)
- [异步编程（25道）](#异步编程)
- [现代JavaScript特性（10道）](#现代javascript特性)

---

## JavaScript基础语法和概念

### 数据类型与变量（15道）

**001. [初级]** 请解释JavaScript中的原始数据类型有哪些？

**002. [初级]** `typeof null` 返回什么值？为什么？

**003. [初级]** 如何判断一个变量是否为数组？

**004. [中级]** 解释`==`和`===`的区别，并举例说明

**005. [初级]** JavaScript中`undefined`和`null`的区别是什么？

**006. [中级]** 什么是类型转换？隐式转换和显式转换有什么区别？

**007. [初级]** `Number('123abc')`和`parseInt('123abc')`的结果分别是什么？

**008. [中级]** 解释JavaScript中的"假值"（falsy values）有哪些？

**009. [初级]** 如何检测一个变量的数据类型？

**010. [中级]** `Object.prototype.toString.call()`方法的作用是什么？

**011. [中级]** JavaScript中的包装对象是什么？

**012. [高级]** 解释JavaScript中的装箱和拆箱操作

**013. [初级]** 什么是NaN？如何检测一个值是否为NaN？

**014. [中级]** `0.1 + 0.2 === 0.3`的结果是什么？为什么？

**015. [中级]** 如何准确地比较两个浮点数是否相等？

### 函数与作用域（15道）

**016. [初级]** 函数声明和函数表达式有什么区别？

**017. [中级]** 什么是函数提升（hoisting）？

**018. [中级]** 解释JavaScript中的作用域链

**019. [初级]** 什么是闭包？请举一个简单的例子

**020. [中级]** 闭包的实际应用场景有哪些？

**021. [高级]** 解释闭包可能导致的内存泄漏问题

**022. [中级]** `this`关键字在不同情况下指向什么？

**023. [中级]** `call`、`apply`、`bind`方法的区别和用法

**024. [初级]** 什么是立即执行函数表达式（IIFE）？

**025. [中级]** 如何实现函数的柯里化（currying）？

**026. [高级]** 什么是尾调用优化？JavaScript支持吗？

**027. [中级]** 函数的`length`属性表示什么？

**028. [初级]** 什么是回调函数？

**029. [中级]** 如何避免回调地狱（callback hell）？

**030. [高级]** 解释函数式编程中的纯函数概念

### 对象与原型（15道）

**031. [初级]** 如何创建一个对象？有几种方式？

**032. [中级]** 什么是原型链？

**033. [中级]** `__proto__`和`prototype`的区别是什么？

**034. [初级]** 如何检查对象是否具有某个属性？

**035. [中级]** `hasOwnProperty`和`in`操作符的区别

**036. [中级]** 如何实现对象的深拷贝？

**037. [高级]** `Object.create()`方法的作用和用法

**038. [中级]** 什么是属性描述符？如何使用？

**039. [初级]** 如何遍历对象的属性？

**040. [中级]** `Object.keys()`、`Object.values()`、`Object.entries()`的区别

**041. [中级]** 如何冻结一个对象？

**042. [高级]** 解释JavaScript中的继承机制

**043. [中级]** 构造函数和普通函数的区别

**044. [中级]** `new`操作符的执行过程是什么？

**045. [高级]** 如何实现一个简单的`new`操作符？

---

## ES6+新特性

### let、const和块级作用域（8道）

**046. [初级]** `let`、`const`和`var`的区别

**047. [初级]** 什么是块级作用域？

**048. [中级]** 什么是暂时性死区（TDZ）？

**049. [中级]** `const`声明的对象可以修改吗？

**050. [初级]** 在什么情况下使用`let`、`const`、`var`？

**051. [中级]** for循环中使用`let`和`var`的区别

**052. [中级]** 如何理解变量提升在ES6中的变化？

**053. [高级]** 解释ES6中函数声明在块级作用域中的行为

### 箭头函数（8道）

**054. [初级]** 箭头函数的基本语法是什么？

**055. [中级]** 箭头函数和普通函数的区别

**056. [中级]** 箭头函数中的`this`指向问题

**057. [初级]** 箭头函数可以作为构造函数吗？

**058. [中级]** 箭头函数没有哪些特性？

**059. [中级]** 什么时候不应该使用箭头函数？

**060. [高级]** 箭头函数在事件处理中的注意事项

**061. [中级]** 如何在箭头函数中访问`arguments`对象？

### 解构赋值（10道）

**062. [初级]** 什么是解构赋值？基本语法是什么？

**063. [初级]** 数组解构赋值的常见用法

**064. [中级]** 对象解构赋值如何设置默认值？

**065. [中级]** 如何解构嵌套对象？

**066. [中级]** 解构赋值在函数参数中的应用

**067. [中级]** 如何交换两个变量的值（使用解构）？

**068. [中级]** 字符串解构赋值的用法

**069. [高级]** 解构赋值的剩余模式（rest pattern）

**070. [中级]** 如何解构赋值并重命名变量？

**071. [中级]** 解构赋值在React props中的应用

### 模板字符串（6道）

**072. [初级]** 什么是模板字符串？如何使用？

**073. [初级]** 模板字符串支持多行吗？

**074. [中级]** 如何在模板字符串中执行函数？

**075. [中级]** 什么是标签模板字符串？

**076. [高级]** 如何实现一个简单的模板字符串处理函数？

**077. [中级]** 模板字符串与字符串拼接的性能比较

### 函数增强（8道）

**078. [初级]** ES6函数参数默认值的用法

**079. [中级]** 剩余参数（rest parameters）和扩展运算符的区别

**080. [中级]** 如何使用扩展运算符合并数组？

**081. [中级]** 扩展运算符在对象中的应用

**082. [中级]** 函数的`name`属性在ES6中的变化

**083. [高级]** 如何使用参数默认值实现必需参数检查？

**084. [中级]** 剩余参数与arguments对象的区别

**085. [中级]** 扩展运算符的实际应用场景

### Class类（12道）

**086. [初级]** ES6中如何定义一个类？

**087. [中级]** 类的构造函数如何定义？

**088. [中级]** 如何实现类的继承？

**089. [中级]** 类中的静态方法如何定义和使用？

**090. [中级]** 类的私有属性如何实现？

**091. [高级]** super关键字的作用和用法

**092. [中级]** 类表达式和类声明的区别

**093. [高级]** 如何在类中定义getter和setter？

**094. [中级]** 类的实例方法和原型方法的区别

**095. [高级]** 类的继承如何实现方法重写？

**096. [中级]** 抽象类在JavaScript中如何模拟？

**097. [高级]** 类的装饰器（decorator）概念及用法

### Symbol数据类型（6道）

**098. [中级]** 什么是Symbol？它的特点是什么？

**099. [中级]** 如何创建Symbol？Symbol的用途有哪些？

**100. [中级]** Symbol.iterator的作用是什么？

**101. [高级]** 如何使用Symbol创建对象的私有属性？

**102. [中级]** Symbol.for()和Symbol()的区别

**103. [中级]** 内置Symbol有哪些？举例说明

### Set和Map（8道）

**104. [初级]** Set数据结构的特点和基本用法

**105. [中级]** 如何使用Set进行数组去重？

**106. [中级]** Map和Object的区别是什么？

**107. [中级]** WeakSet和WeakMap的特点和用途

**108. [中级]** 如何遍历Set和Map？

**109. [高级]** 什么情况下使用WeakMap比Map更合适？

**110. [中级]** Map的键可以是什么类型？

**111. [中级]** 如何将Map转换为数组？

### 迭代器和生成器（4道）

**112. [中级]** 什么是迭代器？如何自定义迭代器？

**113. [中级]** 生成器函数的语法和特点

**114. [高级]** 生成器的实际应用场景有哪些？

**115. [高级]** 如何使用生成器实现无限序列？

---

## 异步编程

### Promise基础（12道）

**116. [初级]** 什么是Promise？它解决了什么问题？

**117. [初级]** Promise有哪几种状态？

**118. [中级]** 如何创建一个Promise？

**119. [中级]** Promise.then()方法的用法

**120. [中级]** Promise的链式调用如何工作？

**121. [中级]** Promise.catch()和try-catch的区别

**122. [中级]** 如何处理多个Promise？

**123. [中级]** Promise.all()和Promise.allSettled()的区别

**124. [高级]** Promise.race()的使用场景

**125. [中级]** 如何取消一个Promise？

**126. [高级]** 如何实现一个简单的Promise？

**127. [中级]** Promise中的错误传播机制

### async/await（8道）

**128. [初级]** async/await的基本用法

**129. [中级]** async函数返回什么？

**130. [中级]** 如何在async函数中处理错误？

**131. [中级]** async/await相比Promise的优势

**132. [中级]** 如何并发执行多个async操作？

**133. [高级]** async/await在循环中的使用注意事项

**134. [中级]** 顶层await的概念和用法

**135. [高级]** 如何实现async/await的polyfill原理？

### 事件循环和微任务（5道）

**136. [中级]** JavaScript的事件循环机制是什么？

**137. [中级]** 宏任务和微任务的区别

**138. [高级]** Promise.resolve()在事件循环中的执行时机

**139. [中级]** setTimeout(0)和Promise.resolve()的执行顺序

**140. [高级]** 如何理解事件循环的执行栈、任务队列和微任务队列？

---

## 现代JavaScript特性

### 模块化（5道）

**141. [中级]** ES6模块与CommonJS模块的区别

**142. [中级]** import和export的各种用法

**143. [中级]** 动态import()的用法和应用场景

**144. [高级]** 模块的循环依赖问题如何解决？

**145. [中级]** Tree-shaking的原理是什么？

### 新API和特性（5道）

**146. [中级]** Proxy对象的作用和基本用法

**147. [高级]** Reflect对象提供了哪些方法？

**148. [中级]** 可选链操作符(?.)的用法

**149. [中级]** 空值合并操作符(??)的作用

**150. [中级]** BigInt数据类型的特点和用法

---

## 前端框架与库（30道）

### React生态（15道）

**151. [中级]** React中的虚拟DOM是什么？它的工作原理是怎样的？

**152. [中级]** React的生命周期方法有哪些？在什么情况下使用？

**153. [高级]** React中的Fiber架构是什么？它解决了什么问题？

**154. [中级]** useState和useEffect的执行时机和注意事项

**155. [中级]** React中如何进行性能优化？（memo、useMemo、useCallback）

**156. [高级]** React的协调算法（Reconciliation）是如何工作的？

**157. [中级]** 受控组件和非受控组件的区别，以及使用场景

**158. [中级]** React中的Context API的使用场景和最佳实践

**159. [高级]** 如何实现一个自定义Hook？有什么设计原则？

**160. [中级]** React中的key属性的作用和使用原则

**161. [高级]** React的事件系统（合成事件）是如何工作的？

**162. [中级]** React Router的实现原理和路由守卫

**163. [高级]** Redux的工作原理，与Context API的区别

**164. [中级]** React中如何处理异步数据加载和错误边界？

**165. [高级]** React 18的新特性：Concurrent Mode和Suspense

### Vue生态（15道）

**166. [中级]** Vue的响应式原理是什么？Vue2和Vue3的区别

**167. [中级]** Vue的生命周期钩子函数及其应用场景

**168. [高级]** Vue3的Composition API相比Options API的优势

**169. [中级]** Vue中的计算属性和侦听器的区别和使用场景

**170. [中级]** Vue组件间通信的方式有哪些？

**171. [高级]** Vue的编译过程是怎样的？模板如何转换为render函数？

**172. [中级]** Vue中的插槽（slot）机制和作用域插槽

**173. [中级]** Vuex的工作原理和模块化管理

**174. [高级]** Vue的虚拟DOM diff算法实现

**175. [中级]** Vue Router的路由模式和导航守卫

**176. [高级]** Vue3的响应式系统：Proxy vs defineProperty

**177. [中级]** Vue中如何进行性能优化？

**178. [高级]** 如何实现一个Vue插件？

**179. [中级]** Vue的服务端渲染（SSR）原理和实现

**180. [高级]** Vue的自定义指令和渲染函数的使用

---

## 浏览器原理与性能优化（25道）

### 浏览器内核与渲染（10道）

**181. [中级]** 浏览器的渲染流程是怎样的？

**182. [中级]** 重排（reflow）和重绘（repaint）的区别和优化

**183. [高级]** 浏览器的多进程架构是什么样的？

**184. [中级]** DOM树和渲染树的区别

**185. [中级]** CSS选择器的解析顺序和性能影响

**186. [高级]** 浏览器的垃圾回收机制和内存泄漏

**187. [中级]** 浏览器缓存机制：强缓存vs协商缓存

**188. [中级]** 浏览器的同源策略和跨域解决方案

**189. [高级]** 浏览器的安全机制：CSP、XSS、CSRF

**190. [中级]** 浏览器的存储方案：localStorage、sessionStorage、IndexedDB

### 性能优化（15道）

**191. [中级]** 前端性能优化的指标有哪些？如何测量？

**192. [中级]** 图片优化的策略和技术方案

**193. [中级]** JavaScript代码分割和懒加载的实现

**194. [高级]** 首屏优化的策略和实践

**195. [中级]** 防抖和节流的实现和应用场景

**196. [中级]** 虚拟列表的实现原理

**197. [高级]** Web Worker的使用场景和实践

**198. [中级]** CDN的工作原理和优化策略

**199. [中级]** 预加载和预连接的技术实现

**200. [高级]** Service Worker的原理和PWA应用

**201. [中级]** 如何优化长列表的渲染性能？

**202. [中级]** Tree Shaking的原理和实践

**203. [高级]** 前端监控和错误追踪的实现

**204. [中级]** 移动端性能优化的特殊考虑

**205. [高级]** 关键渲染路径优化和资源优先级

---

## 前端工程化（20道）

### 构建工具与打包（8道）

**206. [中级]** Webpack的工作原理和核心概念

**207. [中级]** Vite相比Webpack的优势在哪里？

**208. [高级]** 如何编写Webpack插件和Loader？

**209. [中级]** 模块联邦（Module Federation）的概念和应用

**210. [中级]** Babel的工作原理和插件机制

**211. [高级]** AST（抽象语法树）在前端工程化中的应用

**212. [中级]** 如何进行构建产物的分析和优化？

**213. [中级]** 微前端架构的实现方案对比

### 开发工具与规范（6道）

**214. [中级]** Git工作流和团队协作最佳实践

**215. [中级]** ESLint和Prettier的配置和自定义规则

**216. [中级]** 如何设计和维护组件库？

**217. [中级]** 前端测试的策略：单元测试、集成测试、E2E测试

**218. [高级]** CI/CD在前端项目中的实践

**219. [中级]** 代码质量保证：Husky、lint-staged的使用

### 架构设计（6道）

**220. [高级]** 大型前端项目的架构设计思考

**221. [高级]** 微前端的技术选型和实践经验

**222. [中级]** 组件设计的原则和模式

**223. [高级]** 前端状态管理的设计模式

**224. [中级]** 前端路由的设计和实现原理

**225. [高级]** 前端领域驱动设计（DDD）的应用

---

## TypeScript与类型系统（15道）

**226. [初级]** TypeScript相比JavaScript的优势

**227. [中级]** TypeScript的基本类型和高级类型

**228. [中级]** 泛型的使用和约束条件

**229. [高级]** 条件类型和类型推导的应用

**230. [中级]** 接口和类型别名的区别

**231. [高级]** 模板字面量类型的使用

**232. [中级]** 装饰器的原理和应用

**233. [高级]** TypeScript的模块系统和声明文件

**234. [中级]** 如何处理第三方库的类型定义？

**235. [高级]** TypeScript编译器API的使用

**236. [中级]** 类型守卫和类型断言的使用

**237. [高级]** 工具类型（Utility Types）的实现原理

**238. [中级]** TypeScript在React/Vue中的最佳实践

**239. [高级]** 如何设计类型安全的API接口？

**240. [高级]** TypeScript的性能优化和项目配置

---

## 网络与安全（15道）

### 网络协议（8道）

**241. [中级]** HTTP/1.1、HTTP/2、HTTP/3的区别和优化

**242. [中级]** TCP和UDP的区别及在前端中的应用

**243. [中级]** WebSocket的工作原理和使用场景

**244. [中级]** HTTPS的工作原理和性能影响

**245. [高级]** DNS解析过程和优化策略

**246. [中级]** 浏览器的网络请求优化策略

**247. [高级]** HTTP缓存策略的设计和实践

**248. [中级]** GraphQL vs RESTful API的对比

### 前端安全（7道）

**249. [中级]** XSS攻击的类型和防护措施

**250. [中级]** CSRF攻击的原理和防护策略

**251. [中级]** 内容安全策略（CSP）的配置和实践

**252. [高级]** 前端加密和数据保护的实践

**253. [中级]** 点击劫持和防护措施

**254. [高级]** OAuth2和JWT的原理及前端实现

**255. [中级]** 前端权限控制的设计和实现

---

## 数据结构与算法（20道）

### 基础数据结构（8道）

**256. [中级]** 数组、链表、栈、队列的特点和应用

**257. [中级]** 哈希表的实现原理和冲突解决

**258. [中级]** 二叉树的遍历算法实现

**259. [高级]** 平衡二叉树（AVL）的原理

**260. [中级]** 堆的实现和应用场景

**261. [高级]** 图的表示和遍历算法

**262. [中级]** 字符串匹配算法（KMP）

**263. [中级]** 排序算法的时间复杂度对比

### 算法应用（8道）

**264. [中级]** 动态规划的基本思想和经典问题

**265. [中级]** 贪心算法的适用场景

**266. [高级]** 回溯算法在前端中的应用

**267. [中级]** 双指针技巧的使用

**268. [中级]** 滑动窗口算法的实现

**269. [高级]** 分治算法的设计思想

**270. [中级]** 递归和迭代的转换

**271. [高级]** 时间复杂度和空间复杂度分析

### 前端特定算法（4道）

**272. [中级]** 虚拟DOM的diff算法实现

**273. [中级]** LRU缓存算法的实现

**274. [高级]** 前端路由匹配算法

**275. [中级]** 防抖节流算法的优化实现

---

## 移动端与跨平台（10道）

**276. [中级]** 移动端适配方案：rem、vw/vh、flexible

**277. [中级]** 移动端触摸事件和手势识别

**278. [中级]** 混合应用开发：WebView优化

**279. [高级]** React Native的原理和性能优化

**280. [中级]** 小程序的架构原理和开发实践

**281. [中级]** PWA的核心技术和实现

**282. [高级]** Flutter Web的技术特点

**283. [中级]** 移动端性能监控和优化

**284. [中级]** 响应式设计的实现策略

**285. [中级]** 移动端兼容性问题和解决方案

---

## 项目经验与软技能（15道）

### 项目架构（8道）

**286. [高级]** 如何从零搭建一个前端项目？

**287. [高级]** 大型项目的代码组织和模块划分

**288. [中级]** 如何进行技术选型和风险评估？

**289. [高级]** 前后端分离项目的协作模式

**290. [中级]** 如何处理项目中的技术债务？

**291. [高级]** 微服务架构在前端的应用

**292. [中级]** 多团队协作的代码规范制定

**293. [高级]** 前端项目的监控和运维体系

### 软技能（7道）

**294. [中级]** 如何进行代码review？

**295. [中级]** 技术方案设计和评审的流程

**296. [高级]** 如何推动团队技术栈升级？

**297. [中级]** 前端技术趋势的判断和学习

**298. [高级]** 如何平衡业务需求和技术追求？

**299. [中级]** 跨部门沟通和需求理解

**300. [高级]** 技术领导力和团队管理

---

## 完整题目统计

**总计：300道题目**

### 按难度分布：
- 初级（60道）：20%
- 中级（180道）：60%  
- 高级（60道）：20%

### 按内容分布：
- JavaScript基础语法和概念：45道（15%）
- ES6+新特性：70道（23.3%）
- 异步编程：25道（8.3%）
- 现代JavaScript特性：10道（3.3%）
- 前端框架与库：30道（10%）
- 浏览器原理与性能优化：25道（8.3%）
- 前端工程化：20道（6.7%）
- TypeScript与类型系统：15道（5%）
- 网络与安全：15道（5%）
- 数据结构与算法：20道（6.7%）
- 移动端与跨平台：10道（3.3%）
- 项目经验与软技能：15道（5%）

### 新增核心能力考查：
- 框架深度理解与性能优化
- 浏览器原理与工程化思维
- TypeScript类型系统设计
- 网络协议与前端安全
- 算法思维与问题解决
- 架构设计与团队协作

**备注：**
- 新增150道中高级题目，更贴近大厂面试要求
- 涵盖现代前端工程师必备的核心技能
- 注重理论基础与实践应用的结合
- 包含软技能和项目经验相关考查

**总计：150道题目**

### 按难度分布：
- 初级（60道）：40%
- 中级（68道）：45.3%
- 高级（22道）：14.7%

### 按内容分布：
- JavaScript基础语法和概念：45道（30%）
- ES6+新特性：70道（46.7%）
- 异步编程：25道（16.7%）
- 现代JavaScript特性：10道（6.6%）

### 知识点覆盖：
- 数据类型与变量
- 函数与作用域
- 对象与原型
- ES6语法特性
- 异步编程模式
- 现代JavaScript API

**备注：**
- 题目按知识点分类，便于系统学习
- 难度递进，从基础概念到实际应用
- 重点关注企业面试高频考点
- 涵盖现代JavaScript开发必备技能