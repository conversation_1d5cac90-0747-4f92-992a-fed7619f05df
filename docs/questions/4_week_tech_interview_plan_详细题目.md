# 4 周面试训练计划（每日题目详情 + 示例输入输出）

> 本文档基于 `yangshun/tech-interview-handbook` 的 4 周训练计划，把每一天的**主要练题**补充为：题目描述、输入输出格式、示例与约束。每天另附一条可选拓展题（列名）。

---

## 使用说明

- 每日完成 **主题目 1 道**（带例子）为主；若时间充裕，可做当天的**可选题**（难度可调）。
- 每道题建议流程：读题 → 列出思路与复杂度 → 书写解法 → 手写或口述复盘 → 写出 1–2 个边界测试用例。
- 本文档为 Markdown，可直接打印或保存为 `.md`。

---

### 第 1 周 — 基础与常见技巧

#### Day 1（数组与哈希） — 主题目：Two Sum

**难度**：简单

**题目描述**：给定一个整数数组 `nums` 和一个目标值 `target`，请你在数组中找出和为目标值的两个整数，并返回它们的数组下标（任意顺序）。你可以假设每种输入只会对应一个答案，但是，数组中同一个元素不能使用两遍。

**输入**：`nums: number[]`, `target: number`

**输出**：`number[]`（长度为 2 的下标数组）

**示例**：

```
输入: nums = [2,7,11,15], target = 9
输出: [0,1]
说明: nums[0] + nums[1] = 2 + 7 = 9
```

**约束**：`2 <= nums.length <= 10^5`，`-10^9 <= nums[i] <= 10^9`。

**提示**：使用哈希表在一次遍历内完成，时间复杂度 O(n)，空间 O(n)。

**可选题**：数组中两个数之和（变体：输出具体数值对）

---

#### Day 2（双指针） — 主题目：Container With Most Water

**难度**：中等

**题目描述**：给定一个长度为 n 的非负整数数组 `height`，数组中第 i 个元素表示在坐标 i 处的一根垂直线条的高度。找出两条线，使它们与 x 轴一起形成的容器可以容纳最多的水，返回能容纳的最大水量（宽度为索引差，面积受最短边限制）。

**输入**：`height: number[]`

**输出**：`number`（最大面积）

**示例**：

```
输入: height = [1,8,6,2,5,4,8,3,7]
输出: 49
解释: 选择索引 1（高度 8）和索引 8（高度 7），宽度 = 7，面积 = 7 * min(8,7) = 49
```

**约束**：`2 <= height.length <= 10^5`，`0 <= height[i] <= 10^4`。

**提示**：双指针从两端向中间移动，每次移动较短的一侧，时间 O(n)，空间 O(1)。

**可选题**：Trapping Rain Water（接雨水）

---

#### Day 3（字符串 / 滑动窗口） — 主题目：Longest Substring Without Repeating Characters

**难度**：中等

**题目描述**：给定一个字符串 `s`，请找出其中不含重复字符的最长子串的长度。

**输入**：`s: string`

**输出**：`number`（最大长度）

**示例**：

```
输入: s = "abcabcbb"
输出: 3
解释: 最长子串是 "abc"，长度为 3。
```

**约束**：`0 <= s.length <= 10^5`。

**提示**：滑动窗口 + 哈希集合/字典维护字符最后位置，时间 O(n)。

**可选题**：Minimum Window Substring（最小覆盖子串）

---

#### Day 4（哈希进阶） — 主题目：Top K Frequent Elements

**难度**：中等

**题目描述**：给定一个整数数组 `nums` 和一个整数 `k`，返回出现频率前 k 高的元素。答案可以按任意顺序返回。

**输入**：`nums: number[]`, `k: number`

**输出**：`number[]`（含 k 个元素）

**示例**：

```
输入: nums = [1,1,1,2,2,3], k = 2
输出: [1,2]
```

**约束**：`1 <= nums.length <= 10^5`, `k` 总在有效范围内。

**提示**：用哈希表统计频率，然后用堆（或桶排序）取 top-k。平均时间 O(n log k) 或 O(n)。

**可选题**：Top K Frequent Words（词频前 k）

---

#### Day 5（排序 / 二分） — 主题目：Search in Rotated Sorted Array

**难度**：中等

**题目描述**：整数数组 `nums` 原本按升序排列，且其中所有元素互不相同。但在某个未知的索引处旋转（例如 `[0,1,2,4,5,6,7]` 可能变为 `[4,5,6,7,0,1,2]`）。给定旋转后的数组和一个目标值 `target`，若数组中存在目标则返回其索引，否则返回 -1。要求 O(log n) 时间复杂度。

**输入**：`nums: number[]`, `target: number`

**输出**：`number`（索引或 -1）

**示例**：

```
输入: nums = [4,5,6,7,0,1,2], target = 0
输出: 4
```

**约束**：`1 <= nums.length <= 10^5`。

**提示**：变体的二分查找：判断中点在左有序区还是右有序区，决定移动方向。

**可选题**：Find Minimum in Rotated Sorted Array

---

#### Day 6（综合练习） — 主题目：Move Zeroes

**难度**：简单

**题目描述**：给定一个数组 `nums`，编写一个函数将所有 0 移动到末尾，同时保持非零元素的相对顺序。

**输入**：`nums: number[]`（就地修改或返回新数组）

**输出**：`number[]`（非必需，视实现而定）

**示例**：

```
输入: nums = [0,1,0,3,12]
输出: [1,3,12,0,0]
```

**约束**：尽量就地操作，时间 O(n)，空间 O(1)。

**可选题**：Remove Element（移除元素）

---

#### Day 7（周日复盘） — 主任务：复盘 3 道题（写总结）

- 无编码题，建议把本周做过的 3 道题写出：思路、时间/空间复杂度、1-2 个边界测试用例。
- 同时检查并更新一项简历内容。

---

### 第 2 周 — 树与递归

#### Day 8（树遍历） — 主题目：Binary Tree Preorder Traversal

**难度**：简单

**题目描述**：给定二叉树的根节点，返回它的前序遍历（根 → 左 → 右）。

**输入**：`root: TreeNode | null`（二叉树节点, `val,left,right`）

**输出**：`number[]`（遍历顺序数组）

**示例**：

```
输入: [1,null,2,3]
输出: [1,2,3]
```

**约束**：节点数可达 10^4，注意递归深度问题。

**可选题**：Inorder / Postorder Traversal（迭代实现）

---

#### Day 9（BST） — 主题目：Validate Binary Search Tree

**难度**：中等

**题目描述**：给定一个二叉树，判断其是否为合法的二叉搜索树（BST）。BST 定义为：左子树所有节点 < 根节点 < 右子树所有节点，且左右子树也必须为 BST。

**输入**：`root: TreeNode | null`

**输出**：`boolean`

**示例**：

```
输入: [2,1,3]
输出: true

输入: [5,1,4,null,null,3,6]
输出: false
```

**提示**：使用中序遍历检查是否为严格升序，或使用上下界递归。

**可选题**：Lowest Common Ancestor of a BST

---

#### Day 10（BFS） — 主题目：Binary Tree Level Order Traversal

**难度**：简单

**题目描述**：返回二叉树的层序遍历，每一层作为一个子数组。

**输入**：`root: TreeNode | null`

**输出**：`number[][]`

**示例**：

```
输入: [3,9,20,null,null,15,7]
输出: [[3],[9,20],[15,7]]
```

**可选题**：Populating Next Right Pointers in Each Node

---

#### Day 11（图的入门） — 主题目：Number of Islands

**难度**：中等

**题目描述**：给定一个由 `'1'`（陆地）和 `'0'`（水）组成的二维网格，计算网格中岛屿的数量。岛屿由相邻的竖直或水平相邻陆地组成。

**输入**：`grid: char[][]`

**输出**：`number`

**示例**：

```
输入:
[
  ["1","1","0","0","0"],
  ["1","1","0","0","0"],
  ["0","0","1","0","0"],
  ["0","0","0","1","1"]
]
输出: 3
```

**提示**：用 DFS 或 BFS 把访问过的陆地标记为已访问。

**可选题**：Clone Graph（克隆图）

---

#### Day 12（回溯） — 主题目：Letter Combinations of a Phone Number

**难度**：中等

**题目描述**：给定一个仅包含数字 `2-9` 的字符串，返回所有它可以表示的字母组合（按电话按键映射）。可按任意顺序返回答案。

**输入**：`digits: string`

**输出**：`string[]`

**示例**：

```
输入: digits = "23"
输出: ["ad","ae","af","bd","be","bf","cd","ce","cf"]
```

**提示**：回溯生成所有组合。

**可选题**：Generate Parentheses（生成括号）

---

#### Day 13（递归与记忆化） — 主题目：Climbing Stairs

**难度**：简单

**题目描述**：你正在爬楼梯。需要 n 阶你才能到达楼顶。每次你可以爬 1 或 2 个台阶。计算有多少种不同的方法可以爬到楼顶。

**输入**：`n: number`

**输出**：`number`

**示例**：

```
输入: n = 3
输出: 3
解释: (1+1+1), (1+2), (2+1)
```

**提示**：这是 Fibonacci 系列的变体，可用 DP 或记忆化递归。

**可选题**：House Robber（打家劫舍，简单 DP）

---

#### Day 14（周日复盘） — 主任务：60 分钟限时模拟 + 弱点复盘

- 完成一套 60 分钟限时题（1–2 道），记录时间分配与错题，写出改进计划。

---

### 第 3 周 — 中高级算法

#### Day 15（堆） — 主题目：Merge K Sorted Lists

**难度**：中等

**题目描述**：给你 k 个已按升序排序的链表，请将它们合并为一个按升序排序的链表并返回。

**输入**：`lists: ListNode[]`（k 个链表）

**输出**：`ListNode`（合并后的链表头）

**示例**：

```
输入: lists = [[1,4,5],[1,3,4],[2,6]]
输出: [1,1,2,3,4,4,5,6]
```

**提示**：使用最小堆优先队列，时间 O(n log k)，n 是总节点数。

**可选题**：Top K Frequent Elements（如果尚未练习）

---

#### Day 16（贪心） — 主题目：Jump Game

**难度**：中等

**题目描述**：给定一个非负整数数组 `nums`，你最初位于数组的第一个下标。每个元素表示你在该位置可以跳跃的最大长度。判断你是否能够到达最后一个下标。

**输入**：`nums: number[]`

**输出**：`boolean`

**示例**：

```
输入: nums = [2,3,1,1,4]
输出: true

输入: nums = [3,2,1,0,4]
输出: false
```

**提示**：贪心维护能到达的最远位置，时间 O(n)。

**可选题**：Jump Game II（跳跃最少次数）

---

#### Day 17（DP 入门） — 主题目：Longest Increasing Subsequence

**难度**：中等

**题目描述**：给定一个无序整数数组，找到其中最长严格递增子序列的长度。

**输入**：`nums: number[]`

**输出**：`number`

**示例**：

```
输入: nums = [10,9,2,5,3,7,101,18]
输出: 4
解释: 最长递增子序列为 [2,3,7,101]
```

**提示**：经典 O(n^2) DP 或 O(n log n) 的 patience sorting（尾值数组 + 二分）。

**可选题**：Longest Common Subsequence（最长公共子序列）

---

#### Day 18（中等 DP） — 主题目：Coin Change

**难度**：中等

**题目描述**：给定不同面额的硬币 `coins` 和一个总金额 `amount`，编写函数计算组成该金额所需的最少硬币数。如果无法组成，返回 -1。

**输入**：`coins: number[]`, `amount: number`

**输出**：`number`（最少硬币数或 -1）

**示例**：

```
输入: coins = [1,2,5], amount = 11
输出: 3
解释: 11 = 5 + 5 + 1
```

**提示**：完全背包动态规划或 BFS（当作最短路径）。

**可选题**：Unbounded Knapsack（无界背包变体）

---

#### Day 19（位运算 / 数学） — 主题目：Single Number

**难度**：简单

**题目描述**：给定一个非空整数数组，除了某个元素只出现一次以外，其余每个元素均出现两次。找出只出现一次的那个元素。

**输入**：`nums: number[]`

**输出**：`number`

**示例**：

```
输入: nums = [2,2,1]
输出: 1
```

**提示**：使用异或运算快速完成，时间 O(n)，空间 O(1)。

**可选题**：Single Number II（出现三次的变体）

---

#### Day 20（系统化复习） — 主任务：补弱并整理错题本

- 选择前两周或前三周的弱点题型各一题做深度复盘，写出模板化解题步骤。

---

#### Day 21（周日） — 主任务：简历深度打磨 + 模拟面试

- 针对目标公司角色调整简历中的技术关键字与项目结果；进行一次 30–45 分钟模拟面试并记录反馈。

---

### 第 4 周 — 强化与冲刺

#### Day 22（综合） — 主题目：Word Ladder（单词接龙）

**难度**：中等

**题目描述**：给定 `beginWord`、`endWord` 和字典 `wordList`，每次可以改变单词中的一个字母，且新单词必须在字典中。返回从 `beginWord` 到 `endWord` 的最短转换序列长度（若不存在则返回 0）。

**输入**：`beginWord: string`, `endWord: string`, `wordList: string[]`

**输出**：`number`

**示例**：

```
输入: beginWord = "hit", endWord = "cog", wordList = ["hot","dot","dog","lot","log","cog"]
输出: 5
解释: "hit" -> "hot" -> "dot" -> "dog" -> "cog"
```

**提示**：双向 BFS 可将时间复杂度改善。

**可选题**：Word Ladder II（给出所有最短转换序列）

---

#### Day 23（系统设计 / 题型专栏） — 主任务：系统设计入门（阅读并写提纲）

- 若你是前端候选人，可改为：前端常见题（事件循环、闭包、浏览器渲染路径）并写出面试回答要点。

---

#### Day 24（限时模拟） — 主任务：1 小时限时：1\~2 道题并完整复盘

- 以真实面试节奏完成：先口述再编码，记录每题耗时与错题点。

---

#### Day 25（行为面深挖） — 主任务：背诵 10 个行为问题 STAR 答案

- 建议题目：Tell me about a time you failed; Describe a conflict; Lead a project; Impactful decision; etc.

---

#### Day 26（口头演练） — 主任务：白板演练 + 录音回听

- 把 3 道题白板演练并录音，注意语言组织性与边界条件说明。

---

#### Day 27（补弱） — 主题目（任选弱点）

- 选择 2 道你认为最薄弱的题型各做一题，完整写出题解与复杂度证明。

---

#### Day 28（终极模拟） — 主任务：完整面试流程模拟 + 总结

- 模拟一轮完整面试（编码 + 行为 + 结束问题），写出 1 页总结与下阶段学习计划。

---

## 附录：如何把题目映射到 LeetCode / 仓库题单

- 如果你需要，我可以把每道主题题目直接映射到对应 LeetCode 题号，并生成可执行的 **Markdown 题单（含链接）**。

---

*如需我把此文档导出为 **``** 文件并打包，或把题目替换为更高级/更简单的版本，请告诉我你的偏好（例如：初级/中级/高级）。*

