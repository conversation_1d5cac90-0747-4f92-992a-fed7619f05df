# Dependencies
node_modules/
pnpm-lock.yaml
package-lock.json
yarn.lock

# Build outputs
dist/
dist-ssr/
build/
.output/
.nuxt/

# Cache directories
.cache/
.eslintcache
.vite/
.turbo/

# Environment and config files
.env*
*.log

# Auto-generated files
typings/components.d.ts
*.d.ts

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Documentation and markdown (optional - remove if you want to format these)
CHANGELOG*
LICENSE*

# Static assets and public files
public/
static/

# Git
.git/

# Coverage reports
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
.temp/
.tmp/